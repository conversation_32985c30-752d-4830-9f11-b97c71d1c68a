<script setup lang="ts">
import type { SalaryEntry } from '@/gql/graphql'
import { ChangeType } from '@/gql/graphql'
import { useAppStore } from '@/stores/app/appStore'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { formatPercentage } from '@/utils/transformers'

const props = defineProps({
  entries: {
    type: Array as () => SalaryEntry[],
    required: true,
  },
  participantName: {
    type: String,
    required: true,
  },
  employmentInfoId: {
    type: String,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  canEdit: {
    type: Boolean,
    default: true,
  },
  year: {
    type: Number,
    default: 2024,
  },
})

const emit = defineEmits(['refresh'])

const dialogVisible = ref(false)
const selectedEntry = ref<SalaryEntry | null>(null)
const currentYear = new Date().getFullYear()

const appStore = useAppStore()

const pensionStore = usePensionStore()

const certifiedYears = computed(() => {
  return pensionStore.certifiedDataYears
})

const isDisabled = (pendingChanges: string[]) => {
  return pendingChanges.includes('partTimePercentage')
}

const isCurrentYear = (year: number): boolean => {
  return year === currentYear
}

const isCertifiedYear = (year: number) => {
  return certifiedYears.value.includes(year)
}

const getStatusColor = (year: number): string => {
  if (isCurrentYear(year))
    return 'primary'

  return 'success'
}

const openEditDialog = (entry: SalaryEntry) => {
  if (isDisabled(entry?.pendingChanges)) {
    appStore.showSnack('Sorry you cannot edit this field.')

    return
  }
  selectedEntry.value = entry
  dialogVisible.value = true
}
</script>

<template>
  <VCard
    variant="outlined"
    class="mb-4"
  >
    <VCardTitle class="py-3">
      <h4 class="text-subtitle-1 font-weight-medium">
        Part-time Percentage
      </h4>
    </VCardTitle>
    <VCardSubtitle class="text-caption text-medium-emphasis">
      Percentage as of January 1st or start date if started during the year
    </VCardSubtitle>
    <VCardText>
      <VTable
        class="percentage-table"
        density="comfortable"
      >
        <tbody>
          <tr v-if="loading">
            <td
              colspan="2"
              class="text-center"
            >
              <VProgressCircular
                indeterminate
                color="primary"
              />
            </td>
          </tr>
          <tr v-else-if="entries.filter(entry => entry.year === props.year).length === 0">
            <td
              colspan="2"
              class="text-center"
            >
              No part-time percentage data found for {{ props.year }}
            </td>
          </tr>
          <template v-else>
            <tr
              v-for="entry in entries.filter(entry => entry.year === props.year)"
              :key="entry.id"
              :class="isCurrentYear(entry.year) ? 'bg-blue-lighten-5' : ''"
            >
              <th class="text-left">
                PART-TIME %
              </th>
              <td class="d-flex align-center justify-space-between">
                <span>{{ formatPercentage(entry.partTimePercentage) }}</span>
                <div class="d-flex align-center">
                  <VChip
                    v-if="!props.canEdit"
                    :color="getStatusColor(entry.year)"
                    size="small"
                    label
                    class="mr-2"
                  >
                    Certified
                  </VChip>
                  <VBtn
                    v-if="canEdit && !isCertifiedYear(entry.year)"
                    icon
                    size="small"
                    variant="text"
                    color="primary"
                    @click="openEditDialog(entry)"
                  >
                    <VIcon
                      v-if="isDisabled(entry?.pendingChanges)"
                      size="16"
                      icon="tabler-alert-triangle"
                      class="edit-icon"
                      color="error"
                    />
                    <VIcon
                      v-else
                      size="16"
                      icon="tabler-edit"
                      class="edit-icon"
                      color="primary"
                    />
                  </VBtn>
                </div>
              </td>
            </tr>
          </template>
        </tbody>
      </VTable>
    </VCardText>

    <SalaryEntityDialog
      v-model="dialogVisible"
      :edit-mode="true"
      path="partTimePercentage"
      :entity-id="selectedEntry?.id"
      entity-type="SalaryEntry"
      :entry="selectedEntry as any"
      form-type="partTime"
      :participant-name="participantName"
      :type="ChangeType.Participant"
      :year="selectedEntry?.year"
      @refresh="$emit('refresh')"
    />
  </VCard>
</template>

<style scoped>
  .percentage-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px;
  }

  .percentage-table th {
    font-size: 0.75rem;
    letter-spacing: 0.0625rem;
    font-weight: 500;
    background-color: #f5f5f5;
  }

  .bg-blue-lighten-5 {
    background-color: rgba(66, 165, 245, 0.1);
  }
</style>
