<script setup lang="ts">
import { useQuery } from '@vue/apollo-composable'
import { GET_PARTICIPANT_BY_ID } from '@/api/graphql/queries/participantQueries'
import type { SalaryEntry } from '@/gql/graphql'
import { ChangeType } from '@/gql/graphql'
import { useAppStore } from '@/stores/app/appStore'
import { usePensionStore } from '@/stores/pension/pensionStore'

const props = defineProps({
  participantId: {
    type: String,
    required: true,
  },
  employmentInfoId: {
    type: String,
    required: true,
  },
  canEdit: {
    type: Boolean,
    default: true,
  },
  editable: {
    type: Boolean,
    default: true,
  },
  year: {
    type: Number,
    default: 2024,
  },
})

const emit = defineEmits(['refresh'])

const entries = ref<SalaryEntry[]>([])
const loading = ref(true)
const dialogVisible = ref(false)
const participantName = ref('')
const selectedEntry = ref<SalaryEntry | null>(null)
const currentYear = new Date().getFullYear()
const appStore = useAppStore()
const pensionStore = usePensionStore()

const certifiedYears = computed(() => {
  return pensionStore.certifiedDataYears
})

const { result, loading: queryLoading, refetch } = useQuery(
  GET_PARTICIPANT_BY_ID,
  { id: props.participantId },
  { fetchPolicy: 'network-only' },
)

// Format currency values
const formatCurrency = (value: number): string => {
  return value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// Check if entry is from current year
const isCurrentYear = (year: number): boolean => {
  return year === currentYear
}

// Helper function to determine status color
const getStatusColor = (year: number): string => {
  if (isCurrentYear(year))
    return 'primary'

  return 'success'
}

const isDisabled = (pendingChanges: string[]) => {
  return pendingChanges.includes('amount')
}

const isCertifiedYear = (year: number) => {
  return certifiedYears.value.includes(year)
}

const fetchEntries = async () => {
  loading.value = true
  try {
    await refetch()
    if (result.value?.getParticipantById?.employmentInfo?.salaryEntries) {
      // Filter entries by the specified year
      const filteredEntries = result.value.getParticipantById.employmentInfo.salaryEntries.filter((entry: any) => entry.year === props.year)

      entries.value = filteredEntries
      participantName.value = `${result.value.getParticipantById.personalInfo?.firstName} ${result.value.getParticipantById.personalInfo?.lastName}`
    }
    else {
      entries.value = []
    }
    emit('refresh')
  }
  catch (error) {
    console.error('Error fetching salary entries:', error)
    entries.value = []
  }
  finally {
    loading.value = false
  }
}

const openEditDialog = (entry: SalaryEntry) => {
  if (isDisabled(entry.pendingChanges)) {
    appStore.showSnack('Sorry you cannot edit this field.')

    return
  }
  selectedEntry.value = entry
  dialogVisible.value = true
}

onMounted(fetchEntries)
</script>

<template>
  <VCard
    variant="outlined"
    class="mb-4"
  >
    <VCardTitle class="py-3">
      <h4 class="text-subtitle-1 font-weight-medium">
        Gross Part-time Monthly Salary
      </h4>
    </VCardTitle>
    <VCardSubtitle class="text-caption text-medium-emphasis">
      Amount as of January 1st or start date if started during the year
    </VCardSubtitle>
    <VCardText>
      <VTable
        class="salary-table"
        density="comfortable"
      >
        <tbody>
          <tr v-if="loading">
            <td
              colspan="2"
              class="text-center"
            >
              <VProgressCircular
                indeterminate
                color="primary"
              />
            </td>
          </tr>
          <tr v-else-if="entries.length === 0">
            <td
              colspan="2"
              class="text-center"
            >
              No salary entry found for {{ props.year }}
            </td>
          </tr>
          <template v-else>
            <tr
              v-for="entry in entries"
              :key="entry.id"
              :class="isCurrentYear(entry.year) ? 'bg-blue-lighten-5' : ''"
            >
              <th class="text-left">
                GROSS PART-TIME MONTHLY SALARY
              </th>
              <td class="d-flex align-center justify-space-between">
                <span>Afl. {{ formatCurrency(entry.amount) }}</span>
                <div class="d-flex align-center">
                  <VChip
                    v-if="!props.canEdit"
                    :color="getStatusColor(entry.year)"
                    size="small"
                    label
                    class="mr-2"
                  >
                    Certified
                  </VChip>
                  <VBtn
                    v-if="canEdit && !isCertifiedYear(entry.year)"
                    icon
                    size="small"
                    variant="text"
                    color="primary"
                    @click="openEditDialog(entry)"
                  >
                    <VIcon
                      v-if="isDisabled(entry.pendingChanges)"
                      size="16"
                      icon="tabler-alert-triangle"
                      class="edit-icon"
                      color="error"
                    />
                    <VIcon
                      v-else
                      size="16"
                      icon="tabler-edit"
                      class="edit-icon"
                      color="primary"
                    />
                  </VBtn>
                </div>
              </td>
            </tr>
          </template>
        </tbody>
      </VTable>
    </VCardText>

    <SalaryEntityDialog
      v-model="dialogVisible"
      :edit-mode="true"
      path="amount"
      :entity-id="selectedEntry?.id"
      entity-type="SalaryEntry"
      :entry="selectedEntry as any"
      form-type="salary"
      :participant-name="participantName"
      :type="ChangeType.Participant"
      :year="selectedEntry?.year"
      @refresh="fetchEntries"
    />
  </VCard>
</template>

<style scoped>
.salary-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}

.salary-table th {
  font-size: 0.75rem;
  letter-spacing: 0.0625rem;
  font-weight: 500;
  background-color: #f5f5f5;
}

.bg-blue-lighten-5 {
  background-color: rgba(66, 165, 245, 0.1);
}
</style>
